import { Ledger } from '@prisma/client'
import { contextBridge, ipcRenderer } from 'electron'

// Custom APIs for renderer
const api = {
  db: {
    ledger: {
      getLedgers: () => {
        return ipcRenderer.invoke('get-ledgers')
      },
      createLedger: (data: Ledger) => {
        return ipcRenderer.invoke('create-ledger', data)
      }
    }
  }
}

if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', api)
  } catch (error) {
    console.error(error)
  }
}
