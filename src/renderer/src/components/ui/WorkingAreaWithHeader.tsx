import { ReactNode } from 'react'

export interface WorkingAreaWithHeaderProps {
  header: string
  children: ReactNode
  action?: ReactNode
}

const WorkingAreaWithHeader = ({ children, header, action }: WorkingAreaWithHeaderProps) => {
  return (
    <div>
      <div className="pt-8 pb-12 flex items-center justify-between">
        <h1 className="text-4xl font-semibold">{header}</h1>
        {action && <div>{action}</div>}
      </div>
      <div>{children}</div>
    </div>
  )
}

export default WorkingAreaWithHeader
