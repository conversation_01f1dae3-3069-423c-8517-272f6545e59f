import { Avatar, AvatarFallback, AvatarImage } from '@renderer/components/ui/shadcn/avatar'
import { SidebarHeader } from '@renderer/components/ui/shadcn/sidebar'

const SidePanelHeader = () => {
  return (
    <SidebarHeader>
      <div className="flex flex-col items-center gap-3 px-4 pt-6 pb-2">
        <Avatar className="h-20 w-20 border-2 border-sidebar-accent">
          <AvatarImage src="/placeholder.svg" alt="Avatar" />
          <AvatarFallback className="text-2xl bg-sidebar-accent text-sidebar-accent-foreground">NR</AvatarFallback>
        </Avatar>
        <div className="flex flex-col text-center">
          <span className="text-base font-semibold text-sidebar-foreground">NR Jewellers</span>
          <span className="text-sm text-sidebar-foreground/70">Premium Account</span>
        </div>
      </div>
    </SidebarHeader>
  )
}

export default SidePanelHeader
