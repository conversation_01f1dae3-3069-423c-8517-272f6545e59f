import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@renderer/components/ui/shadcn/sidebar'
import { BookOpen, FileText, IndianRupee, LayoutDashboard, ShoppingBag } from 'lucide-react'
import { useLocation, NavLink } from 'react-router'

const SidePanelContent = () => {
  const location = useLocation()
  const currentPath = location.pathname

  const items = [
    {
      title: 'Dashboard',
      path: '/dashboard',
      icon: <LayoutDashboard className="h-4 w-4" />
    },
    {
      title: 'Ledger',
      path: '/ledger',
      icon: <BookOpen className="h-4 w-4" />
    },
    {
      title: 'Sales',
      path: '/sales',
      icon: <IndianRupee className="h-4 w-4" />
    },
    {
      title: 'Purchases',
      path: '/purchases',
      icon: <ShoppingBag className="h-4 w-4" />
    },
    {
      title: 'Balance Sheet',
      path: '/balance-sheet',
      icon: <FileText className="h-4 w-4" />
    }
  ]

  return (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            {items.map((item) => (
              <SidebarMenuItem key={item.title}>
                <NavLink to={item.path}>
                  <SidebarMenuButton isActive={currentPath === item.path}>
                    {item.icon}
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </NavLink>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )
}

export default SidePanelContent
