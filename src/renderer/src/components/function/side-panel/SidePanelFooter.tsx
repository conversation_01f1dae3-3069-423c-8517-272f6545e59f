import { Button } from '@renderer/components/ui/shadcn/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@renderer/components/ui/shadcn/dropdown-menu'
import { SidebarFooter } from '@renderer/components/ui/shadcn/sidebar'
import { Plus, ChevronUp, ShoppingBag, CreditCard, IndianRupee, BookOpen } from 'lucide-react'
import { NavLink } from 'react-router'

const SidePanelFooter = () => {
  const items = [
    {
      title: 'Ledger',
      icon: <BookOpen className="text-accent-foreground mr-2 h-4 w-4" />,
      path: '/add/ledger'
    },
    {
      title: 'Purchase',
      icon: <ShoppingBag className="text-accent-foreground mr-2 h-4 w-4" />,
      path: '/add/purchase'
    },
    {
      title: 'Sale',
      icon: <IndianRupee className="text-accent-foreground mr-2 h-4 w-4" />,
      path: '/add/sale'
    },
    {
      title: 'Receipt',
      icon: <CreditCard className="text-accent-foreground mr-2 h-4 w-4" />,
      path: '/add/receipt'
    },
    {
      title: 'Payment',
      icon: <CreditCard className="text-accent-foreground mr-2 h-4 w-4" />,
      path: '/add/payment'
    }
  ]

  return (
    <SidebarFooter className="p-4">
      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button className="w-full justify-between">
            <div className="flex items-center">
              <Plus className="mr-2 h-4 w-4" />
              Add
            </div>
            <ChevronUp className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          {items.map((item) => (
            <NavLink key={item.title} to={item.path}>
              <DropdownMenuItem className="flex items-center">
                {item.icon}
                <span>{item.title}</span>
              </DropdownMenuItem>
            </NavLink>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarFooter>
  )
}

export default SidePanelFooter
