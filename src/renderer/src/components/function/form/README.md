# Reusable Form Components

This directory contains reusable form components built with shadcn UI and react-hook-form to create consistent forms across the application.

## Components

### FormWrapper

A container component that wraps your form with shadcn's Form component.

```tsx
<FormWrapper
  form={form}
  onSubmit={handleSubmit}
>
  {/* Form fields go here */}
</FormWrapper>
```

**Note:** This component should be used inside a layout component like `WorkingAreaWithHeader`:

```tsx
<WorkingAreaWithHeader header="Add New Item">
  <FormWrapper form={form} onSubmit={handleSubmit}>
    {/* Form fields go here */}
  </FormWrapper>
</WorkingAreaWithHeader>
```

### Form Fields

#### TextField

For text input fields.

```tsx
<TextField
  control={form.control}
  name="name"
  label="Name"
  placeholder="Enter name"
  required
/>
```

#### NumberField

For numeric input fields.

```tsx
<NumberField
  control={form.control}
  name="amount"
  label="Amount"
  placeholder="0.00"
  required
/>
```

#### SelectField

For dropdown select fields.

```tsx
<SelectField
  control={form.control}
  name="category"
  label="Category"
  options={[
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' }
  ]}
  placeholder="Select a category"
  required
/>
```

#### CustomField

For custom input components.

```tsx
<CustomField
  control={form.control}
  name="customField"
  label="Custom Field"
  required
  renderInput={(field) => (
    <YourCustomComponent {...field} />
  )}
/>
```

### FormActions

For form action buttons (submit and cancel).

```tsx
<FormActions
  form={form}
  onCancel={handleCancel}
  submitLabel="Save Item"
/>
```

## Usage Example

```tsx
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  FormWrapper,
  TextField,
  NumberField,
  SelectField,
  FormActions
} from '@renderer/components/function/form'
import WorkingAreaWithHeader from '@renderer/components/ui/WorkingAreaWithHeader'

// Define schema
const formSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  amount: z.coerce.number().min(0),
  category: z.string().min(1)
})

type FormValues = z.infer<typeof formSchema>

const MyFormPage = () => {
  const navigate = useNavigate()

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      amount: 0,
      category: ''
    }
  })

  const onSubmit = async (data: FormValues) => {
    // Handle form submission
    console.log(data)
    navigate('/some-route')
  }

  const handleCancel = () => navigate('/some-route')

  return (
    <WorkingAreaWithHeader header="My Form">
      <FormWrapper form={form} onSubmit={onSubmit}>
        <TextField
          control={form.control}
          name="name"
          label="Name"
          required
        />

        <NumberField
          control={form.control}
          name="amount"
          label="Amount"
          required
        />

        <SelectField
          control={form.control}
          name="category"
          label="Category"
          options={[
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' }
          ]}
          required
        />

        <FormActions
          form={form}
          onCancel={handleCancel}
        />
      </FormWrapper>
    </WorkingAreaWithHeader>
  )
}
```
