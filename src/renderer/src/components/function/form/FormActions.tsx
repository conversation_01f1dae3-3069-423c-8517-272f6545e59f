import { Button } from '@renderer/components/ui/shadcn/button'
import { FieldValues, UseFormReturn } from 'react-hook-form'
import { Loader2 } from 'lucide-react'

interface FormActionsProps<TFormValues extends FieldValues> {
  form: UseFormReturn<TFormValues>
  onCancel: () => void
  submitLabel?: string
  cancelLabel?: string
  className?: string
  isLoading?: boolean
}

export function FormActions<TFormValues extends FieldValues>({
  form,
  onCancel,
  submitLabel = 'Save',
  cancelLabel = 'Cancel',
  className = 'flex justify-end gap-2 pt-4',
  isLoading
}: Readonly<FormActionsProps<TFormValues>>) {
  return (
    <div className={className}>
      <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading || form.formState.isSubmitting}>
        {cancelLabel}
      </Button>
      <Button type="submit" disabled={isLoading || form.formState.isSubmitting}>
        {isLoading || form.formState.isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Saving...
          </>
        ) : (
          submitLabel
        )}
      </Button>
    </div>
  )
}
