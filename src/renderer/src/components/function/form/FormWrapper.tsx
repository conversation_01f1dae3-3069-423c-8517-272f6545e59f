import { ReactNode } from 'react'
import { Form } from '@renderer/components/ui/shadcn/form'
import { UseFormReturn } from 'react-hook-form'

interface FormWrapperProps<TFormValues> {
  form: UseFormReturn<TFormValues>
  onSubmit: (data: TFormValues) => Promise<void>
  children: ReactNode
  className?: string
}

export function FormWrapper<TFormValues>({
  form,
  onSubmit,
  children,
  className = 'max-w-lg space-y-6'
}: Readonly<FormWrapperProps<TFormValues>>) {
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={className}>
        {children}
      </form>
    </Form>
  )
}
