import { Button } from '@renderer/components/ui/shadcn/button'
import { Moon, Sun } from 'lucide-react'
import { useTheme } from '@renderer/hooks/use-theme'

const ThemeToggleButton = () => {
  const { theme, setTheme } = useTheme()

  return (
    <Button
      size="icon"
      className="h-8 w-8"
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
    >
      <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}

export default ThemeToggleButton
