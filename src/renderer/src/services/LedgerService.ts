import { Ledger } from '@prisma/client'

type CreateLedgerInput = Omit<Ledger, 'id' | 'createdAt' | 'updatedAt'>

class LedgerService {
  async createLedger(ledger: CreateLedgerInput) {
    const data = await window.electron.db.ledger.createLedger(ledger)
    return data;
  }

  async getLedgers() {
    const data = await window.electron.db.ledger.getLedgers()
    return data;
  }
}

export default new LedgerService();