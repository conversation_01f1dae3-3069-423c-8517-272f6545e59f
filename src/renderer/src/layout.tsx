import SidePanel from '@renderer/components/function/side-panel'
import { Outlet } from 'react-router'
import { SidebarProvider } from '@renderer/components/ui/shadcn/sidebar'
import ThemeToggleButton from '@renderer/components/function/ThemeToggleButton'
import { Toaster } from '@renderer/components/ui/shadcn/sonner'

const Layout = () => {
  return (
    <SidebarProvider>
      <div className="flex h-screen w-full bg-background">
        <div className="w-64">
          <SidePanel />
        </div>
        <main className="relative flex-1 overflow-y-auto p-6 w-full">
          <div className="absolute right-4 top-4">
            <ThemeToggleButton />
          </div>
          <Outlet />
        </main>
      </div>
      <Toaster />
    </SidebarProvider>
  )
}

export default Layout
