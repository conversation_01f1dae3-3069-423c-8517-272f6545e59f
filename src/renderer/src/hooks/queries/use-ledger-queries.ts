import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Ledger } from '@prisma/client'
import LedgerService from '@renderer/services/LedgerService'

type ICreateLedger = Omit<Ledger, 'id' | 'createdAt' | 'updatedAt'>

// Query keys for caching and invalidation
export const ledgerKeys = {
  all: ['ledgers'] as const,
  lists: () => [...ledgerKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...ledgerKeys.lists(), { filters }] as const,
  details: () => [...ledgerKeys.all, 'detail'] as const,
  detail: (id: number) => [...ledgerKeys.details(), id] as const
}

// Hook for fetching all ledgers
export function useLedgers() {
  return useQuery({
    queryKey: ledgerKeys.lists(),
    queryFn: () => LedgerService.getLedgers()
  })
}

// Hook for creating a new ledger
export function useCreateLedger() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ledger: ICreateLedger) => LedgerService.createLedger(ledger),
    onSuccess: () => {
      // Invalidate the ledgers list query to refetch data
      queryClient.invalidateQueries({ queryKey: ledgerKeys.lists() })
    }
  })
}
