import React from 'react'
import ReactDOM from 'react-dom/client'
import './styles/globals.css'
import { createBrowserRouter, RouterProvider } from 'react-router'
import LoginPage from './pages/login'
import ProtectedRoute from './components/function/ProtectedRoute'
import DashboardPage from './pages/dashboard'
import Layout from '@renderer/layout'
import LedgerPage from './pages/ledger'
import SalesPage from './pages/sales'
import PurchasesPage from './pages/purchases'
import BalanceSheetPage from './pages/balance-sheet'
import AddLedgerPage from './pages/add/Ledger'
import AddPurchasePage from './pages/add/Purchase'
import AddSalePage from './pages/add/Sale'
import AddReceiptPage from './pages/add/Receipt'
import AddPaymentPage from './pages/add/Payment'
import { QueryProvider } from './providers/QueryProvider'

const router = createBrowserRouter([
  {
    path: '/login',
    Component: LoginPage
  },
  {
    Component: Layout,
    children: [
      {
        Component: ProtectedRoute,
        children: [
          {
            path: '/dashboard',
            Component: DashboardPage
          },
          {
            path: '/ledger',
            Component: LedgerPage
          },
          {
            path: '/sales',
            Component: SalesPage
          },
          {
            path: '/purchases',
            Component: PurchasesPage
          },
          {
            path: '/balance-sheet',
            Component: BalanceSheetPage
          },
          {
            path: '/add',
            children: [
              {
                path: 'ledger',
                Component: AddLedgerPage
              },
              {
                path: 'purchase',
                Component: AddPurchasePage
              },
              {
                path: 'sale',
                Component: AddSalePage
              },
              {
                path: 'receipt',
                Component: AddReceiptPage
              },
              {
                path: 'payment',
                Component: AddPaymentPage
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '*',
    Component: LoginPage
  }
])

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <QueryProvider>
      <RouterProvider router={router} />
    </QueryProvider>
  </React.StrictMode>
)
