import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormWrapper, TextField, NumberField, SelectField, FormActions } from '@renderer/components/function/form'
import WorkingAreaWithHeader from '@renderer/components/ui/WorkingAreaWithHeader'

// Define the form schema using Zod
const formSchema = z.object({
  customerName: z.string().min(1, { message: 'Customer name is required' }),
  invoiceNumber: z.string().min(1, { message: 'Invoice number is required' }),
  amount: z.coerce.number().min(0, { message: 'Amount must be a positive number' }),
  paymentMethod: z.enum(['CASH', 'CREDIT', 'BANK_TRANSFER'], { 
    required_error: 'Please select a payment method' 
  }),
  itemDescription: z.string().min(1, { message: 'Item description is required' }),
  notes: z.string().optional()
})

// Infer the form data type from the schema
type FormValues = z.infer<typeof formSchema>

const AddSalePage = () => {
  const navigate = useNavigate()

  // Initialize the form with react-hook-form and zod resolver
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      customerName: '',
      invoiceNumber: '',
      amount: 0,
      paymentMethod: undefined,
      itemDescription: '',
      notes: ''
    },
    mode: 'onChange'
  })

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      // This would be replaced with an actual service call
      console.log('Sale data:', data)
      
      // Navigate to sales list page after successful creation
      navigate('/sales')
    } catch (error) {
      console.error('Error creating sale:', error)
    }
  }

  // Handle cancel button click
  const handleCancel = () => {
    navigate('/sales')
  }

  // Payment method options for the select field
  const paymentMethodOptions = [
    { value: 'CASH', label: 'Cash' },
    { value: 'CREDIT', label: 'Credit' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' }
  ]

  return (
    <WorkingAreaWithHeader header="Add New Sale">
      <FormWrapper form={form} onSubmit={onSubmit}>
        <TextField 
          control={form.control} 
          name="customerName" 
          label="Customer Name" 
          placeholder="Enter customer name" 
          required 
        />
        
        <TextField 
          control={form.control} 
          name="invoiceNumber" 
          label="Invoice Number" 
          placeholder="Enter invoice number" 
          required
        />
        
        <TextField 
          control={form.control} 
          name="itemDescription" 
          label="Item Description" 
          placeholder="Enter item description" 
          required
        />
        
        <NumberField 
          control={form.control} 
          name="amount" 
          label="Amount" 
          placeholder="0.00" 
          required 
        />
        
        <SelectField 
          control={form.control} 
          name="paymentMethod" 
          label="Payment Method" 
          options={paymentMethodOptions} 
          placeholder="Select payment method" 
          required 
        />
        
        <TextField 
          control={form.control} 
          name="notes" 
          label="Notes" 
          placeholder="Enter any additional notes" 
        />
        
        <FormActions 
          form={form} 
          onCancel={handleCancel} 
          submitLabel="Save Sale" 
        />
      </FormWrapper>
    </WorkingAreaWithHeader>
  )
}

export default AddSalePage
