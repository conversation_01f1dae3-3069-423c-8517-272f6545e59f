import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormWrapper, TextField, NumberField, SelectField, FormActions } from '@renderer/components/function/form'
import WorkingAreaWithHeader from '@renderer/components/ui/WorkingAreaWithHeader'

// Define the form schema using Zod
const formSchema = z.object({
  receivedFrom: z.string().min(1, { message: 'Received from is required' }),
  receiptNumber: z.string().min(1, { message: 'Receipt number is required' }),
  amount: z.coerce.number().min(0, { message: 'Amount must be a positive number' }),
  paymentMethod: z.enum(['CASH', 'CHEQUE', 'BANK_TRANSFER', 'UPI'], { 
    required_error: 'Please select a payment method' 
  }),
  date: z.string().min(1, { message: 'Date is required' }),
  notes: z.string().optional()
})

// Infer the form data type from the schema
type FormValues = z.infer<typeof formSchema>

const AddReceiptPage = () => {
  const navigate = useNavigate()

  // Initialize the form with react-hook-form and zod resolver
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      receivedFrom: '',
      receiptNumber: '',
      amount: 0,
      paymentMethod: undefined,
      date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
      notes: ''
    },
    mode: 'onChange'
  })

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      // This would be replaced with an actual service call
      console.log('Receipt data:', data)
      
      // Navigate back to the appropriate page after successful creation
      navigate('/ledger')
    } catch (error) {
      console.error('Error creating receipt:', error)
    }
  }

  // Handle cancel button click
  const handleCancel = () => {
    navigate('/ledger')
  }

  // Payment method options for the select field
  const paymentMethodOptions = [
    { value: 'CASH', label: 'Cash' },
    { value: 'CHEQUE', label: 'Cheque' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
    { value: 'UPI', label: 'UPI' }
  ]

  return (
    <WorkingAreaWithHeader header="Add New Receipt">
      <FormWrapper form={form} onSubmit={onSubmit}>
        <TextField 
          control={form.control} 
          name="receivedFrom" 
          label="Received From" 
          placeholder="Enter name" 
          required 
        />
        
        <TextField 
          control={form.control} 
          name="receiptNumber" 
          label="Receipt Number" 
          placeholder="Enter receipt number" 
          required
        />
        
        <TextField 
          control={form.control} 
          name="date" 
          label="Date" 
          type="date"
          required
        />
        
        <NumberField 
          control={form.control} 
          name="amount" 
          label="Amount" 
          placeholder="0.00" 
          required 
        />
        
        <SelectField 
          control={form.control} 
          name="paymentMethod" 
          label="Payment Method" 
          options={paymentMethodOptions} 
          placeholder="Select payment method" 
          required 
        />
        
        <TextField 
          control={form.control} 
          name="notes" 
          label="Notes" 
          placeholder="Enter any additional notes" 
        />
        
        <FormActions 
          form={form} 
          onCancel={handleCancel} 
          submitLabel="Save Receipt" 
        />
      </FormWrapper>
    </WorkingAreaWithHeader>
  )
}

export default AddReceiptPage
