import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'
import { Group } from '@prisma/client'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  FormWrapper,
  TextField,
  NumberField,
  SelectField,
  FormActions
} from '@renderer/components/function/form'
import WorkingAreaWithHeader from '@renderer/components/ui/WorkingAreaWithHeader'
import { useCreateLedger } from '@renderer/hooks/queries/use-ledger-queries'
import { toast } from 'sonner'
import { wait } from '@renderer/lib/utils'

// Define the form schema using Zod
const formSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  mobileNumber: z.string().optional(),
  openingBalance: z.coerce
    .number()
    .min(0, { message: 'Opening balance must be a positive number' }),
  group: z.nativeEnum(Group, { required_error: 'Please select a group' })
})

// Infer the form data type from the schema
type FormValues = z.infer<typeof formSchema>

const AddLedgerPage = () => {
  const navigate = useNavigate()
  const createLedger = useCreateLedger()

  // Initialize the form with react-hook-form and zod resolver
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      mobileNumber: '',
      openingBalance: 0,
      group: undefined
    },
    mode: 'onChange'
  })

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    await wait(1000) // Wait for 1 second for better UX
    createLedger.mutate(
      {
        name: data.name,
        mobileNumber: data.mobileNumber ?? '',
        openingBalance: data.openingBalance,
        group: data.group
      },
      {
        onSuccess: () => {
          toast.success('Ledger created successfully')
          navigate('/ledger')
        },
        onError: (error) => {
          console.error('Error creating ledger:', error)
          toast.error('Failed to create ledger. Please try again later.')
        }
      }
    )
  }

  // Handle cancel button click
  const handleCancel = () => {
    navigate('/ledger')
  }

  // Group options for the select field
  const groupOptions = [
    { value: Group.CREDITORS, label: 'Creditor' },
    { value: Group.DEBTORS, label: 'Debtor' },
    { value: Group.CASH, label: 'Cash' },
    { value: Group.LOAN, label: 'Loan' }
  ]

  return (
    <WorkingAreaWithHeader header="Add New Ledger">
      <FormWrapper form={form} onSubmit={onSubmit}>
        <TextField
          control={form.control}
          name="name"
          label="Name"
          placeholder="Enter ledger name"
          required
          disabled={createLedger.isPending}
        />

        <TextField
          control={form.control}
          name="mobileNumber"
          label="Mobile Number"
          placeholder="Enter mobile number"
          disabled={createLedger.isPending}
        />

        <NumberField
          control={form.control}
          name="openingBalance"
          label="Opening Balance"
          placeholder="0.00"
          required
          disabled={createLedger.isPending}
        />

        <SelectField
          control={form.control}
          name="group"
          label="Group"
          options={groupOptions}
          placeholder="Select a group"
          required
          disabled={createLedger.isPending}
        />

        <FormActions
          form={form}
          onCancel={handleCancel}
          submitLabel="Save Ledger"
          isLoading={createLedger.isPending}
        />
      </FormWrapper>
    </WorkingAreaWithHeader>
  )
}

export default AddLedgerPage
