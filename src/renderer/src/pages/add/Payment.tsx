import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormWrapper, TextField, NumberField, SelectField, FormActions } from '@renderer/components/function/form'
import WorkingAreaWithHeader from '@renderer/components/ui/WorkingAreaWithHeader'

// Define the form schema using Zod
const formSchema = z.object({
  paidTo: z.string().min(1, { message: 'Paid to is required' }),
  paymentNumber: z.string().min(1, { message: 'Payment number is required' }),
  amount: z.coerce.number().min(0, { message: 'Amount must be a positive number' }),
  paymentMethod: z.enum(['CASH', 'CHEQUE', 'BANK_TRANSFER', 'UPI'], { 
    required_error: 'Please select a payment method' 
  }),
  date: z.string().min(1, { message: 'Date is required' }),
  category: z.enum(['SUPPLIER', 'EXPENSE', 'SALARY', 'OTHER'], {
    required_error: 'Please select a category'
  }),
  notes: z.string().optional()
})

// Infer the form data type from the schema
type FormValues = z.infer<typeof formSchema>

const AddPaymentPage = () => {
  const navigate = useNavigate()

  // Initialize the form with react-hook-form and zod resolver
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      paidTo: '',
      paymentNumber: '',
      amount: 0,
      paymentMethod: undefined,
      date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
      category: undefined,
      notes: ''
    },
    mode: 'onChange'
  })

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      // This would be replaced with an actual service call
      console.log('Payment data:', data)
      
      // Navigate back to the appropriate page after successful creation
      navigate('/ledger')
    } catch (error) {
      console.error('Error creating payment:', error)
    }
  }

  // Handle cancel button click
  const handleCancel = () => {
    navigate('/ledger')
  }

  // Payment method options for the select field
  const paymentMethodOptions = [
    { value: 'CASH', label: 'Cash' },
    { value: 'CHEQUE', label: 'Cheque' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
    { value: 'UPI', label: 'UPI' }
  ]

  // Category options for the select field
  const categoryOptions = [
    { value: 'SUPPLIER', label: 'Supplier' },
    { value: 'EXPENSE', label: 'Expense' },
    { value: 'SALARY', label: 'Salary' },
    { value: 'OTHER', label: 'Other' }
  ]

  return (
    <WorkingAreaWithHeader header="Add New Payment">
      <FormWrapper form={form} onSubmit={onSubmit}>
        <TextField 
          control={form.control} 
          name="paidTo" 
          label="Paid To" 
          placeholder="Enter name" 
          required 
        />
        
        <TextField 
          control={form.control} 
          name="paymentNumber" 
          label="Payment Number" 
          placeholder="Enter payment number" 
          required
        />
        
        <TextField 
          control={form.control} 
          name="date" 
          label="Date" 
          type="date"
          required
        />
        
        <NumberField 
          control={form.control} 
          name="amount" 
          label="Amount" 
          placeholder="0.00" 
          required 
        />
        
        <SelectField 
          control={form.control} 
          name="paymentMethod" 
          label="Payment Method" 
          options={paymentMethodOptions} 
          placeholder="Select payment method" 
          required 
        />
        
        <SelectField 
          control={form.control} 
          name="category" 
          label="Category" 
          options={categoryOptions} 
          placeholder="Select category" 
          required 
        />
        
        <TextField 
          control={form.control} 
          name="notes" 
          label="Notes" 
          placeholder="Enter any additional notes" 
        />
        
        <FormActions 
          form={form} 
          onCancel={handleCancel} 
          submitLabel="Save Payment" 
        />
      </FormWrapper>
    </WorkingAreaWithHeader>
  )
}

export default AddPaymentPage
