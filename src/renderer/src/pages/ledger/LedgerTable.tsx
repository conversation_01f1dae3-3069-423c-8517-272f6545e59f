import { Group } from '@prisma/client'
import { DataTable } from '@renderer/components/ui/data-table'
import { columns } from './columns'

interface LedgerTableProps {
  data: any[]
  isLoading: boolean
  error: Error | null
}

export const LedgerTable = ({ data, isLoading, error }: LedgerTableProps) => {
  // Filter options for the group column
  const groupFilterOptions = Object.values(Group).map(group => ({
    label: group.charAt(0) + group.slice(1).toLowerCase(),
    value: group
  }))

  if (isLoading) {
    return <div className="flex justify-center py-8">Loading ledgers...</div>
  }
  
  if (error) {
    return (
      <div className="flex justify-center py-8 text-red-500">
        Error loading ledgers: {error instanceof Error ? error.message : 'Unknown error'}
      </div>
    )
  }
  
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      filterableColumns={[
        {
          id: 'group',
          title: 'Group',
          options: groupFilterOptions
        }
      ]}
    />
  )
}
