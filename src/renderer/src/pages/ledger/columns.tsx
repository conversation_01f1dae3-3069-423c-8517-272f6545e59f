import { ColumnDef } from '@tanstack/react-table'
import { Ledger } from '@prisma/client'
import { ArrowUpDown, MoreHorizontal } from 'lucide-react'
import { But<PERSON> } from '@renderer/components/ui/shadcn/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@renderer/components/ui/shadcn/dropdown-menu'
import { Badge } from '@renderer/components/ui/shadcn/badge'

export const columns: ColumnDef<Ledger>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          <span>Name</span>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="ml-1 h-6 w-6"
          >
            <ArrowUpDown className="h-3 w-3" />
            <span className="sr-only">Sort by name</span>
          </Button>
        </div>
      )
    },
    cell: ({ row }) => <div>{row.getValue('name')}</div>
  },
  {
    accessorKey: 'mobileNumber',
    header: 'Mobile Number',
    cell: ({ row }) => <div>{row.getValue('mobileNumber') || '-'}</div>
  },
  {
    accessorKey: 'openingBalance',
    header: ({ column }) => {
      return (
        <div className="flex items-center justify-end">
          <span>Opening Balance</span>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="ml-1 h-6 w-6"
          >
            <ArrowUpDown className="h-3 w-3" />
            <span className="sr-only">Sort by opening balance</span>
          </Button>
        </div>
      )
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('openingBalance'))
      const formatted = new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
      }).format(amount)

      return <div className="text-right font-medium">{formatted}</div>
    }
  },
  {
    accessorKey: 'group',
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          <span>Group</span>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="ml-1 h-6 w-6"
          >
            <ArrowUpDown className="h-3 w-3" />
            <span className="sr-only">Sort by group</span>
          </Button>
        </div>
      )
    },
    cell: ({ row }) => {
      const group = row.getValue('group')

      if (typeof group === 'string') {
        return <Badge variant="secondary">{group.charAt(0) + group.slice(1).toLowerCase()}</Badge>
      }
      return null
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    }
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const ledger = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => navigator.clipboard.writeText(ledger.id.toString())}>
              Copy ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View details</DropdownMenuItem>
            <DropdownMenuItem>Edit</DropdownMenuItem>
            <DropdownMenuItem>Delete</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  }
]
