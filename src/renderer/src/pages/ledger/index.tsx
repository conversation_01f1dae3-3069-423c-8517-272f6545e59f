import WorkingAreaWithHeader from '@renderer/components/ui/WorkingAreaWithHeader'
import { Button } from '@renderer/components/ui/shadcn/button'
import { PlusCircle, RefreshCw } from 'lucide-react'
import { useNavigate } from 'react-router'
import { useLedgers } from '@renderer/hooks/queries/use-ledger-queries'
import { LedgerTable } from './LedgerTable'

const LedgerPage = () => {
  const navigate = useNavigate()
  const { data: ledgers = [], isLoading, error, refetch } = useLedgers()

  const handleAddLedger = () => {
    navigate('/add/ledger')
  }



  return (
    <WorkingAreaWithHeader
      header="Ledgers"
      action={
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleAddLedger}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Ledger
          </Button>
        </div>
      }
    >
      <LedgerTable
        data={ledgers}
        isLoading={isLoading}
        error={error}
      />
    </WorkingAreaWithHeader>
  )
}

export default LedgerPage
