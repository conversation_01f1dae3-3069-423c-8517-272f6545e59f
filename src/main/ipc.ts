import { ipcMain, IpcMainInvokeEvent } from 'electron'
import ledgerService from './db/services/ledger.js'
import { Ledger } from '@prisma/client'

const handleIPCMain = (
  eventName: string,
  handler: (event: IpcMainInvokeEvent, data: any) => Promise<any>
) => {
  ipcMain.handle(eventName, (event, data) => {
    // validate the event
    return handler(event, data)
  })
}

const handleIPC = () => {
  handleIPCMain('create-ledger', async (_: IpcMainInvokeEvent, data: Ledger) => {
    const ledger = await ledgerService.createLedger(data)
    return ledger
  })

  handleIPCMain('get-ledgers', async (_: IpcMainInvokeEvent) => {
    const ledgers = await ledgerService.getLedgers()
    return ledgers
  })
}

export default handleIPC
