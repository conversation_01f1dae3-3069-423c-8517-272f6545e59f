import path from 'path'
import sqlite3 from '@journeyapps/sqlcipher'
import Prisma from '@prisma/client'

// Dynamic key derived from user-specific runtime data
function getEncryptionKey(): string {
  const username = process.env.USER ?? process.env.USERNAME ?? 'default'
  const machineId = require('node-machine-id').machineIdSync()
  return `${username}-${machineId}`.substring(0, 32) // must be 32 bytes for AES-256
}

const isDev = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV
const getProjectRoot = () => {
  if (isDev) {
    return process.cwd()
  } else {
    return path.join(__dirname, '..', '..', '..', '..')
  }
}

export const dbPath = path.join(getProjectRoot(), 'prisma', 'dev.db')
export const encryptionKey = getEncryptionKey()

sqlite3.verbose()
const db = new sqlite3.Database(dbPath)

db.serialize(() => {
  db.run(`PRAGMA key = '${encryptionKey}'`)
  db.run('PRAGMA cipher_compatibility = 4') // SQLCipher v4
})

const prisma = new Prisma.PrismaClient({
  datasources: {
    db: {
      url: `file:${dbPath}?cipher=sqlcipher&key=${encryptionKey}`
    }
  }
})

export default prisma
