{"name": "jeweller-solution", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "type": "module", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@hookform/resolvers": "^5.0.1", "@journeyapps/sqlcipher": "^5.3.1", "@prisma/client": "^6.6.0", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.74.7", "@tanstack/react-query-devtools": "^5.74.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "electron-updater": "^6.3.9", "lucide-react": "^0.487.0", "next-themes": "^0.4.6", "node-machine-id": "^1.1.12", "react-hook-form": "^7.56.1", "react-router": "^7.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "zod": "^3.24.3"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.13.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "electron": "^35.0.3", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.23.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.3", "prisma": "^6.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.8.2", "vite": "^6.2.3"}}