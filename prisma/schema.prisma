generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db" // This is replaced dynamically at runtime
}

model User {
  id    Int    @id @default(autoincrement())
  name  String
  email String @unique
}

model Ledger {
  id             Int      @id @default(autoincrement())
  name           String
  mobileNumber   String
  openingBalance Int
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  group          Group
}

enum Group {
  CREDITORS
  DEBTORS
  CASH
  LOAN
}
